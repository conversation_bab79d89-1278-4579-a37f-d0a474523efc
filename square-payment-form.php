<?php
require_once "includes/square-gateway.php";
include "header.php";

// Get payment parameters
$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";
$preset_service = $_GET["service"] ?? "";

// Initialize Square gateway to get config
$squareGateway = new Square_Gateway();
$squareConfig = $squareGateway->getWebPaymentsConfig();
?>

<main>
  <section class="hero">
    <h1><?php echo $is_donation ? "Make a Donation with Square" : "Pay with Credit Card"; ?></h1>
  </section>

  <section class="section" id="square-payment-form">
    <div style="max-width: 600px; margin: 0 auto;">

      <!-- Payment Summary -->
      <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
        <h3>💳 Square Payment</h3>
        <p>Secure credit card processing powered by Square. Your payment information is encrypted and PCI compliant.</p>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
          <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
            <h4>💳 Accepted Cards</h4>
            <p style="font-size: 1.5em; margin: 10px 0;">💳 💰 🍎 🤖</p>
            <small>Visa, Mastercard, Amex, Discover, Apple Pay, Google Pay</small>
          </div>
          <div style="background: white; padding: 15px; border-radius: 8px; text-align: center;">
            <h4>🔒 Security</h4>
            <p style="font-size: 1.5em; margin: 10px 0;">🛡️ 🔐</p>
            <small>PCI DSS Compliant, 256-bit SSL Encryption</small>
          </div>
        </div>
      </div>

      <!-- Payment Form -->
      <form id="payment-form" style="background: white; padding: 30px; border-radius: 10px; border: 1px solid #ddd;">

        <!-- Payment Details -->
        <div style="margin-bottom: 25px;">
          <h3>Payment Details</h3>

          <div style="margin-bottom: 20px;">
            <label for="amount" style="display: block; margin-bottom: 5px; font-weight: bold;">Amount (USD):</label>
            <input type="number" id="amount" name="amount" min="1" step="0.01" required
                   value="<?php echo htmlspecialchars($preset_amount ?: '10'); ?>"
                   style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px; font-size: 1.1em;">
            <small style="color: #666;">Minimum: $1.00 USD</small>
          </div>

          <div style="margin-bottom: 20px;">
            <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Address:</label>
            <input type="email" id="email" name="email" required
                   style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px;"
                   placeholder="<EMAIL>">
            <small style="color: #666;">For payment confirmation and receipt</small>
          </div>

          <?php if (!$is_donation): ?>
          <div style="margin-bottom: 20px;">
            <label for="service" style="display: block; margin-bottom: 5px; font-weight: bold;">Service Type:</label>
            <select id="service" name="service" required
                    style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px;">
              <option value="consulting" <?php echo ($preset_service === 'consulting') ? 'selected' : ''; ?>>Crypto & Forex Consulting ($150/hour)</option>
              <option value="mining-pool" <?php echo ($preset_service === 'mining-pool') ? 'selected' : ''; ?>>Mining Pool Membership ($200/year)</option>
              <option value="mining-services" <?php echo ($preset_service === 'mining-services') ? 'selected' : ''; ?>>Mining Services ($500/month)</option>
              <option value="analysis" <?php echo ($preset_service === 'analysis') ? 'selected' : ''; ?>>Market Analysis Report ($99/report)</option>
              <option value="other" <?php echo ($preset_service === 'other') ? 'selected' : ''; ?>>Other Services</option>
            </select>
          </div>
          <?php endif; ?>

          <div style="margin-bottom: 25px;">
            <label for="description" style="display: block; margin-bottom: 5px; font-weight: bold;">Description/Notes:</label>
            <textarea id="description" name="description" rows="3"
                      style="width: 100%; padding: 12px; border: 1px solid #ccc; border-radius: 5px;"
                      placeholder="<?php echo $is_donation ? "Optional message with your donation" : "Describe the service you are paying for"; ?>"></textarea>
          </div>

          <!-- Hidden field for payment type -->
          <input type="hidden" name="payment_type" value="<?php echo htmlspecialchars($payment_type); ?>">
        </div>

        <!-- Square Payment Section -->
        <?php
        $amount = floatval($preset_amount ?: 10);
        echo $squareGateway->generatePaymentForm($amount, 'USD', [
            'payment_type' => $payment_type,
            'show_customer_fields' => false // We have our own fields above
        ]);
        ?>
      </form>

      <!-- Security Information -->
      <div style="background: #e7f3ff; padding: 20px; border-radius: 10px; margin-top: 30px;">
        <h4>🔒 Your Security is Our Priority</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
          <div>
            <strong>🛡️ PCI DSS Compliant:</strong>
            <p>All card data is processed securely by Square and never stored on our servers.</p>
          </div>
          <div>
            <strong>🔐 256-bit SSL Encryption:</strong>
            <p>Your payment information is encrypted during transmission using bank-level security.</p>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="multi-gateway-payment-form.php" style="color: #0077cc; text-decoration: none;">← Back to Payment Options</a> |
        <a href="index.php" style="color: #0077cc; text-decoration: none;">Homepage</a>
      </div>
    </div>
  </section>
</main>

<!-- Square Web Payments SDK and JavaScript are now handled by the gateway generatePaymentForm() method -->

<?php include "footer.php"; ?>
