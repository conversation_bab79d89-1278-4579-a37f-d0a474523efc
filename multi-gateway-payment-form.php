<?php
require_once "includes/payment-gateway-manager.php";
include "header.php";

$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";

$gatewayManager = new Payment_Gateway_Manager();
$availableGateways = $gatewayManager->getAvailableGateways();
?>

<main>
  <section class="hero">
    <div class="hero-content">
      <h1><?php echo $is_donation ? "💝 Make a Cryptocurrency Donation" : "💳 Pay with Cryptocurrency"; ?></h1>
      <p><?php echo $is_donation ? "Support our sustainable mining mission with secure crypto donations" : "Secure cryptocurrency payments for our professional services"; ?></p>
      <div class="flex gap-md justify-center mt-xl">
        <a href="#payment-form" class="cta-button">💳 Start Payment</a>
        <a href="#gateway-comparison" class="cta-button secondary">🔍 Compare Options</a>
      </div>
    </div>
  </section>

  <!-- Payment Stats -->
  <section class="section">
    <div class="three-col">
      <div class="card text-center">
        <h3>🔒 100%</h3>
        <p class="text-muted">Secure Processing</p>
      </div>
      <div class="card text-center">
        <h3>⚡ Instant</h3>
        <p class="text-muted">Lightning Network</p>
      </div>
      <div class="card text-center">
        <h3>🌍 300+</h3>
        <p class="text-muted">Cryptocurrencies</p>
      </div>
    </div>
  </section>

  <section class="section" id="payment-form">
    <!-- Payment Process Steps -->
    <div class="text-center mb-xl">
      <h2>💳 Complete Your Payment</h2>
      <p class="text-muted">Follow these simple steps to complete your secure transaction</p>

      <div class="payment-steps">
        <div class="step active" id="step-1">
          <div class="step-number">1</div>
          <div class="step-title">Choose Gateway</div>
        </div>
        <div class="step-connector"></div>
        <div class="step" id="step-2">
          <div class="step-number">2</div>
          <div class="step-title">Select Currency</div>
        </div>
        <div class="step-connector"></div>
        <div class="step" id="step-3">
          <div class="step-number">3</div>
          <div class="step-title">Payment Details</div>
        </div>
        <div class="step-connector"></div>
        <div class="step" id="step-4">
          <div class="step-number">4</div>
          <div class="step-title">Complete</div>
        </div>
      </div>
    </div>

    <?php
    // Display error messages if any
    if (isset($_GET['error'])) {
        $error = $_GET['error'];
        $details = $_GET['details'] ?? '';

        echo '<div class="alert alert-error">';
        echo '<h3>❌ Payment Error</h3>';
        echo '<p>' . htmlspecialchars($error) . '</p>';
        if ($details) echo '<p><strong>Details:</strong> ' . htmlspecialchars($details) . '</p>';
        echo '</div>';
    }
    ?>

    <!-- Payment Form Container -->
    <div class="payment-form-container">
      <form method="POST" action="multi-gateway-process-payment.php" class="payment-form">
        <input type="hidden" name="payment_type" value="<?php echo htmlspecialchars($payment_type); ?>">

        <!-- Step 1: Gateway Selection -->
        <div class="payment-step-card active" id="gateway-step">
          <div class="step-header">
            <div class="step-icon">🔧</div>
            <div>
              <h3>Step 1: Choose Payment Gateway</h3>
              <p class="text-muted">Select your preferred payment processor</p>
            </div>
          </div>

          <div class="gateway-grid">
            <?php foreach ($availableGateways as $gatewayId => $gateway): ?>
            <div class="gateway-card" onclick="selectGateway('<?php echo $gatewayId; ?>')">
              <input type="radio" name="gateway" value="<?php echo $gatewayId; ?>" id="gateway_<?php echo $gatewayId; ?>"
                     style="display: none;" <?php echo $gatewayId === 'btcpay' ? 'checked' : ''; ?>>
              <label for="gateway_<?php echo $gatewayId; ?>">
                <div class="gateway-icon"><?php echo $gateway['icon']; ?></div>
                <h4><?php echo $gateway['name']; ?></h4>
                <p class="gateway-description">
                  <?php echo count($gateway['supported_currencies']) === 1 ? 'Bitcoin Only' : count($gateway['supported_currencies']) . '+ Cryptocurrencies'; ?>
                </p>
                <div class="gateway-feature">
                  <?php echo $gatewayId === 'btcpay' ? '⚡ Lightning Network' : '🌍 Multi-Currency'; ?>
                </div>
              </label>
            </div>
            <?php endforeach; ?>
          </div>

          <!-- Gateway Details -->
          <div id="gateway-details" class="gateway-details">
            <div id="gateway-info-content"></div>
          </div>
        </div>

        <!-- Step 2: Currency Selection -->
        <div class="payment-step-card" id="currency-step">
          <div class="step-header">
            <div class="step-icon">💰</div>
            <div>
              <h3>Step 2: Choose Cryptocurrency</h3>
              <p class="text-muted">Select your preferred digital currency</p>
            </div>
          </div>

          <div class="currency-selector">
            <select id="currency" name="currency" required class="form-input form-select currency-select">
              <option value="">Select cryptocurrency...</option>
              <!-- Options will be populated by JavaScript based on gateway selection -->
            </select>
            <div class="currency-info">
              <small class="text-muted">💡 Available currencies depend on selected payment gateway</small>
            </div>
          </div>
        </div>

        <!-- Step 3: Service & Payment Details -->
        <div class="payment-step-card" id="details-step">
          <div class="step-header">
            <div class="step-icon">📋</div>
            <div>
              <h3>Step 3: Service & Payment Details</h3>
              <p class="text-muted">Choose your service and confirm payment amount</p>
            </div>
          </div>

          <div class="service-selection-section">
            <?php if (!$is_donation): ?>
            <!-- Service Selection First -->
            <div class="service-grid">
              <h4>💼 Choose Your Service</h4>
              <div class="service-cards">
                <div class="service-card" data-service="consulting" data-amount="150" data-type="hourly">
                  <div class="service-icon">💡</div>
                  <h5>Crypto & Forex Consulting</h5>
                  <div class="service-price">$150/hour</div>
                  <p class="service-description">Expert guidance on cryptocurrency and forex trading strategies</p>
                  <div class="service-features">
                    <span class="feature-tag">1-on-1 Session</span>
                    <span class="feature-tag">Strategy Planning</span>
                  </div>
                </div>

                <div class="service-card" data-service="mining-pool" data-amount="200" data-type="yearly">
                  <div class="service-icon">⛏️</div>
                  <h5>Mining Pool Membership</h5>
                  <div class="service-price">$200/year</div>
                  <p class="service-description">Join our exclusive mining pool with premium benefits</p>
                  <div class="service-features">
                    <span class="feature-tag">Annual Access</span>
                    <span class="feature-tag">Priority Support</span>
                  </div>
                </div>

                <div class="service-card" data-service="mining-services" data-amount="500" data-type="monthly">
                  <div class="service-icon">🔧</div>
                  <h5>Mining Services</h5>
                  <div class="service-price">$500/month</div>
                  <p class="service-description">Full-service mining management and optimization</p>
                  <div class="service-features">
                    <span class="feature-tag">Monthly Service</span>
                    <span class="feature-tag">24/7 Monitoring</span>
                  </div>
                </div>

                <div class="service-card" data-service="analysis" data-amount="99" data-type="report">
                  <div class="service-icon">📊</div>
                  <h5>Market Analysis Report</h5>
                  <div class="service-price">$99/report</div>
                  <p class="service-description">Comprehensive market analysis and trading insights</p>
                  <div class="service-features">
                    <span class="feature-tag">Detailed Report</span>
                    <span class="feature-tag">Market Insights</span>
                  </div>
                </div>
              </div>
            </div>
            <?php else: ?>
            <!-- Donation/Investment Options -->
            <div class="investment-grid">
              <h4>💰 Choose Your Contribution</h4>
              <div class="investment-cards">
                <div class="investment-card" data-service="donation-supporter" data-amount="50">
                  <div class="investment-icon">🌱</div>
                  <h5>Supporter</h5>
                  <div class="investment-price">$50+</div>
                  <p class="investment-description">Support our renewable energy research</p>
                </div>

                <div class="investment-card" data-service="donation-advocate" data-amount="500">
                  <div class="investment-icon">🌿</div>
                  <h5>Advocate</h5>
                  <div class="investment-price">$500+</div>
                  <p class="investment-description">Fund equipment upgrades</p>
                </div>

                <div class="investment-card" data-service="donation-champion" data-amount="2500">
                  <div class="investment-icon">🌳</div>
                  <h5>Champion</h5>
                  <div class="investment-price">$2,500+</div>
                  <p class="investment-description">Major renewable energy impact</p>
                </div>

                <div class="investment-card" data-service="investment-equity" data-amount="10000">
                  <div class="investment-icon">🤝</div>
                  <h5>Strategic Investment</h5>
                  <div class="investment-price">$10,000+</div>
                  <p class="investment-description">10% equity stake opportunity</p>
                </div>

                <div class="investment-card" data-service="custom" data-amount="0">
                  <div class="investment-icon">🎯</div>
                  <h5>Custom Amount</h5>
                  <div class="investment-price">Any Amount</div>
                  <p class="investment-description">Choose your own contribution</p>
                </div>
              </div>
            </div>
            <?php endif; ?>

            <!-- Hidden service input for form submission -->
            <input type="hidden" id="service" name="service" value="">
          </div>

          <div class="payment-details-grid">
            <div class="detail-group">
              <label for="amount" class="form-label required">💰 Amount (USD)</label>
              <div class="input-group">
                <div class="input-group-addon">$</div>
                <input type="number" id="amount" name="amount" min="1" step="0.01" required
                       value="<?php echo htmlspecialchars($preset_amount); ?>"
                       class="form-input amount-input" placeholder="0.00">
              </div>
              <div class="amount-info">
                <small class="text-muted" id="amount-description">Select a service to see pricing</small>
              </div>
            </div>

            <div class="detail-group">
              <label for="email" class="form-label required">📧 Email Address</label>
              <input type="email" id="email" name="email" required
                     class="form-input" placeholder="<EMAIL>">
              <small class="text-muted">For payment confirmation and receipt</small>
            </div>

            <div class="detail-group full-width">
              <label for="description" class="form-label">📝 Description/Notes</label>
              <textarea id="description" name="description" rows="4"
                        class="form-input form-textarea"
                        placeholder="<?php echo $is_donation ? "Optional message with your contribution..." : "Additional details about your service requirements..."; ?>"></textarea>
            </div>
          </div>
        </div>

        <!-- Step 4: Review & Submit -->
        <div class="payment-step-card" id="submit-step">
          <div class="step-header">
            <div class="step-icon">🚀</div>
            <div>
              <h3>Step 4: Complete Payment</h3>
              <p class="text-muted">Review your details and proceed to secure payment</p>
            </div>
          </div>

          <div class="payment-summary">
            <div class="summary-item">
              <span class="summary-label">Gateway:</span>
              <span class="summary-value" id="selected-gateway">BTCPay Server</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Currency:</span>
              <span class="summary-value" id="selected-currency">Bitcoin (BTC)</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Amount:</span>
              <span class="summary-value" id="summary-amount">$0.00 USD</span>
            </div>
          </div>

          <div class="submit-section">
            <button type="submit" class="payment-submit-btn">
              <span class="btn-icon">🚀</span>
              <span class="btn-text">Proceed to Secure Payment</span>
            </button>
            <div class="security-notice">
              <small class="text-muted">🔒 Your payment is secured with enterprise-grade encryption</small>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Security & Support -->
    <div class="payment-footer">
      <div class="two-col">
        <div class="card">
          <h4>🔒 Security Features</h4>
          <ul style="list-style: none; padding: 0;">
            <li class="mb-xs">🛡️ Enterprise-grade encryption</li>
            <li class="mb-xs">⚡ Lightning Network support</li>
            <li class="mb-xs">🔐 Self-hosted BTCPay Server</li>
            <li class="mb-xs">🌍 Global payment processing</li>
          </ul>
        </div>
        <div class="card">
          <h4>💡 Need Help?</h4>
          <p class="text-muted">Our support team is available 24/7 to assist with your payment.</p>
          <div class="mt-md">
            <a href="contact.php" class="cta-button secondary">📞 Contact Support</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="text-center mt-xl">
      <div class="flex gap-md justify-center">
        <a href="index.php" class="text-muted">← Back to Homepage</a>
        <span class="text-muted">|</span>
        <a href="services.php" class="text-muted">View Services</a>
      </div>
    </div>
  </section>
</main>

<script>
// Gateway and currency data
const gatewayCurrencies = {
  'btcpay': [
    {value: 'BTC', text: '₿ Bitcoin (BTC) - Lightning & On-chain'}
  ],
  'nowpayments': [
    {value: 'btc', text: '₿ Bitcoin (BTC)'},
    {value: 'eth', text: 'Ξ Ethereum (ETH)'},
    {value: 'usdt', text: '₮ Tether (USDT)'},
    {value: 'usdc', text: '$ USD Coin (USDC)'},
    {value: 'ltc', text: 'Ł Litecoin (LTC)'},
    {value: 'bch', text: '₿ Bitcoin Cash (BCH)'},
    {value: 'trx', text: 'T Tron (TRX)'},
    {value: 'bnb', text: 'B Binance Coin (BNB)'},
    {value: 'ada', text: '₳ Cardano (ADA)'},
    {value: 'dot', text: '● Polkadot (DOT)'},
    {value: 'xrp', text: 'X Ripple (XRP)'},
    {value: 'matic', text: 'M Polygon (MATIC)'}
  ]
};

function selectGateway(gatewayId) {
  // Update radio button
  document.getElementById('gateway_' + gatewayId).checked = true;

  // Update visual selection
  document.querySelectorAll('.gateway-card').forEach(card => {
    card.classList.remove('selected');
  });

  // Highlight selected option
  event.currentTarget.classList.add('selected');

  // Update summary
  const gatewayNames = {
    'btcpay': 'BTCPay Server',
    'nowpayments': 'NowPayments'
  };
  document.getElementById('selected-gateway').textContent = gatewayNames[gatewayId];

  // Show gateway details
  showGatewayDetails(gatewayId);

  // Update currency options
  updateCurrencyOptions(gatewayId);

  // Activate next step
  activateStep(2);
}

function showGatewayDetails(gatewayId) {
  const detailsDiv = document.getElementById('gateway-details');
  const contentDiv = document.getElementById('gateway-info-content');

  const gatewayInfo = {
    'btcpay': {
      title: '⚡ BTCPay Server',
      description: 'Self-hosted Bitcoin payment processor with Lightning Network support',
      features: ['No transaction fees', 'Lightning Network instant payments', 'Self-hosted & private', 'Bitcoin only']
    },
    'nowpayments': {
      title: '🌐 NowPayments',
      description: 'Multi-cryptocurrency payment gateway with global support',
      features: ['300+ cryptocurrencies', 'Low fees (0.5-1.5%)', 'Global support', 'Easy integration']
    }
  };

  if (gatewayInfo[gatewayId]) {
    const info = gatewayInfo[gatewayId];
    contentDiv.innerHTML = `
      <div class="alert alert-info">
        <h4>${info.title}</h4>
        <p>${info.description}</p>
        <ul style="list-style: none; padding: 0; margin-top: var(--space-sm);">
          ${info.features.map(feature => `<li style="margin-bottom: var(--space-xs);">✅ ${feature}</li>`).join('')}
        </ul>
      </div>
    `;
    detailsDiv.classList.add('show');
  }
}

function activateStep(stepNumber) {
  // Update step indicators
  document.querySelectorAll('.step').forEach((step, index) => {
    if (index + 1 <= stepNumber) {
      step.classList.add('active');
    } else {
      step.classList.remove('active');
    }
  });

  // Update step cards
  document.querySelectorAll('.payment-step-card').forEach((card, index) => {
    if (index + 1 === stepNumber) {
      card.classList.add('active');
    } else {
      card.classList.remove('active');
    }
  });
}

function updatePaymentSummary() {
  // Update currency in summary
  const currencySelect = document.getElementById('currency');
  if (currencySelect.value) {
    document.getElementById('selected-currency').textContent = currencySelect.options[currencySelect.selectedIndex].text;
    activateStep(3);
  }

  // Update amount in summary
  const amountInput = document.getElementById('amount');
  if (amountInput.value) {
    document.getElementById('summary-amount').textContent = '$' + parseFloat(amountInput.value).toFixed(2) + ' USD';
    activateStep(4);
  }
}

function updateCurrencyOptions(gatewayId) {
  const currencySelect = document.getElementById('currency');
  currencySelect.innerHTML = '<option value="">Select cryptocurrency...</option>';

  if (gatewayCurrencies[gatewayId]) {
    gatewayCurrencies[gatewayId].forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.value;
      option.textContent = currency.text;
      currencySelect.appendChild(option);
    });
  }
}

// Initialize with default gateway and add event listeners
document.addEventListener('DOMContentLoaded', function() {
  const defaultGateway = document.querySelector('input[name="gateway"]:checked').value;
  updateCurrencyOptions(defaultGateway);

  // Initialize with default gateway selected
  const defaultCard = document.querySelector(`input[value="${defaultGateway}"]`).closest('.gateway-card');
  if (defaultCard) {
    defaultCard.classList.add('selected');
    showGatewayDetails(defaultGateway);
  }

  // Add event listeners for form interactions
  document.getElementById('currency').addEventListener('change', updatePaymentSummary);
  document.getElementById('amount').addEventListener('input', updatePaymentSummary);

  // Initialize summary with default values
  updatePaymentSummary();
});
</script>

<style>
/* Payment Form Structure */
.payment-form-container {
  max-width: 800px;
  margin: 0 auto;
}

.payment-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

/* Payment Steps */
.payment-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  margin: var(--space-xl) 0;
  padding: var(--space-lg);
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  opacity: 0.5;
  transition: all var(--transition-base);
}

.step.active {
  opacity: 1;
  color: var(--primary-color);
}

.step-number {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all var(--transition-base);
}

.step.active .step-number {
  background: var(--primary-gradient);
  color: white;
  transform: scale(1.1);
}

.step-title {
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}

.step-connector {
  width: 3rem;
  height: 2px;
  background: var(--border-color);
  opacity: 0.5;
}

/* Payment Step Cards */
.payment-step-card {
  background: var(--surface-color);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.payment-step-card.active {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.step-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-color);
}

.step-icon {
  font-size: 2rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  border-radius: 50%;
  color: white;
}

/* Gateway Grid */
.gateway-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin: var(--space-lg) 0;
}

.gateway-card {
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-base);
  background: var(--surface-color);
}

.gateway-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.gateway-card input[type="radio"]:checked + label .gateway-card,
.gateway-card.selected {
  border-color: var(--primary-color);
  background: var(--hover-bg);
  box-shadow: var(--shadow-md);
}

.gateway-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-sm);
}

.gateway-description {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin: var(--space-sm) 0;
}

.gateway-feature {
  font-size: 0.75rem;
  color: var(--primary-color);
  font-weight: 500;
}

/* Currency Selector */
.currency-selector {
  max-width: 400px;
  margin: 0 auto;
}

.currency-select {
  font-size: 1.1rem;
  padding: var(--space-lg);
}

.currency-info {
  text-align: center;
  margin-top: var(--space-md);
}

/* Payment Details Grid */
.payment-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
}

.detail-group.full-width {
  grid-column: 1 / -1;
}

.detail-group {
  display: flex;
  flex-direction: column;
}

.amount-input {
  font-size: 1.2rem;
  font-weight: 600;
}

/* Payment Summary */
.payment-summary {
  background: var(--hover-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
  border-bottom: none;
  font-weight: 600;
  font-size: 1.1rem;
}

.summary-label {
  color: var(--text-muted);
}

.summary-value {
  color: var(--text-primary);
  font-weight: 500;
}

/* Submit Section */
.submit-section {
  text-align: center;
}

.payment-submit-btn {
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-lg) var(--space-xl);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  min-width: 250px;
  justify-content: center;
}

.payment-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-icon {
  font-size: 1.2rem;
}

.security-notice {
  margin-top: var(--space-md);
}

/* Payment Footer */
.payment-footer {
  margin-top: var(--space-xl);
  padding-top: var(--space-xl);
  border-top: 1px solid var(--border-color);
}

/* Gateway Details */
.gateway-details {
  margin-top: var(--space-lg);
  display: none;
}

.gateway-details.show {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-steps {
    flex-direction: column;
    gap: var(--space-sm);
  }

  .step-connector {
    width: 2px;
    height: 2rem;
  }

  .gateway-grid {
    grid-template-columns: 1fr;
  }

  .payment-details-grid {
    grid-template-columns: 1fr;
  }

  .step-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<?php include "footer.php"; ?>
