<?php
require_once "includes/payment-gateway-manager.php";
include "header.php";

$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";

$gatewayManager = new Payment_Gateway_Manager();
$availableGateways = $gatewayManager->getAvailableGateways();
?>

<main>
  <section class="hero">
    <div class="hero-content">
      <h1><?php echo $is_donation ? "💝 Make a Cryptocurrency Donation" : "💳 Pay with Cryptocurrency"; ?></h1>
      <p><?php echo $is_donation ? "Support our sustainable mining mission with secure crypto donations" : "Secure cryptocurrency payments for our professional services"; ?></p>
      <div class="flex gap-md justify-center mt-xl">
        <a href="#payment-form" class="cta-button">💳 Start Payment</a>
        <a href="#gateway-comparison" class="cta-button secondary">🔍 Compare Options</a>
      </div>
    </div>
  </section>

  <!-- Payment Stats -->
  <section class="section">
    <div class="three-col">
      <div class="card text-center">
        <h3>🔒 100%</h3>
        <p class="text-muted">Secure Processing</p>
      </div>
      <div class="card text-center">
        <h3>⚡ Instant</h3>
        <p class="text-muted">Lightning Network</p>
      </div>
      <div class="card text-center">
        <h3>🌍 300+</h3>
        <p class="text-muted">Cryptocurrencies</p>
      </div>
    </div>
  </section>

  <section class="section" id="payment-form">
    <!-- Payment Process Steps -->
    <div class="text-center mb-xl">
      <h2>💳 Complete Your Payment</h2>
      <p class="text-muted">Follow these simple steps to complete your secure transaction</p>

      <div class="payment-steps">
        <div class="step active" id="step-1">
          <div class="step-number">1</div>
          <div class="step-title">Choose Gateway</div>
        </div>
        <div class="step-connector"></div>
        <div class="step" id="step-2">
          <div class="step-number">2</div>
          <div class="step-title">Select Currency</div>
        </div>
        <div class="step-connector"></div>
        <div class="step" id="step-3">
          <div class="step-number">3</div>
          <div class="step-title">Payment Details</div>
        </div>
        <div class="step-connector"></div>
        <div class="step" id="step-4">
          <div class="step-number">4</div>
          <div class="step-title">Complete</div>
        </div>
      </div>
    </div>

    <?php
    // Display error messages if any
    if (isset($_GET['error'])) {
        $error = $_GET['error'];
        $details = $_GET['details'] ?? '';

        echo '<div class="alert alert-error">';
        echo '<h3>❌ Payment Error</h3>';
        echo '<p>' . htmlspecialchars($error) . '</p>';
        if ($details) echo '<p><strong>Details:</strong> ' . htmlspecialchars($details) . '</p>';
        echo '</div>';
    }
    ?>

    <!-- Payment Form Container -->
    <div class="payment-form-container">
      <form method="POST" action="multi-gateway-process-payment.php" class="payment-form">
        <input type="hidden" name="payment_type" value="<?php echo htmlspecialchars($payment_type); ?>">

        <!-- Step 1: Gateway Selection -->
        <div class="payment-step-card active" id="gateway-step">
          <div class="step-header">
            <div class="step-icon">🔧</div>
            <div>
              <h3>Step 1: Choose Payment Gateway</h3>
              <p class="text-muted">Select your preferred payment processor</p>
            </div>
          </div>

          <div class="gateway-grid">
            <?php foreach ($availableGateways as $gatewayId => $gateway): ?>
            <div class="gateway-card" data-gateway="<?php echo $gatewayId; ?>" onclick="selectGateway('<?php echo $gatewayId; ?>')">
              <input type="radio" name="gateway" value="<?php echo $gatewayId; ?>" id="gateway_<?php echo $gatewayId; ?>"
                     style="display: none;" <?php echo $gatewayId === 'btcpay' ? 'checked' : ''; ?>>
              <label for="gateway_<?php echo $gatewayId; ?>">
                <div class="gateway-icon"><?php echo $gateway['icon']; ?></div>
                <h4><?php echo $gateway['name']; ?></h4>
                <p class="gateway-description">
                  <?php echo count($gateway['supported_currencies']) === 1 ? 'Bitcoin Only' : count($gateway['supported_currencies']) . '+ Cryptocurrencies'; ?>
                </p>
                <div class="gateway-feature">
                  <?php echo $gatewayId === 'btcpay' ? '⚡ Lightning Network' : '🌍 Multi-Currency'; ?>
                </div>
              </label>
            </div>
            <?php endforeach; ?>
          </div>

          <!-- Gateway Details -->
          <div id="gateway-details" class="gateway-details">
            <div id="gateway-info-content"></div>
          </div>
        </div>

        <!-- Step 2: Currency Selection -->
        <div class="payment-step-card" id="currency-step">
          <div class="step-header">
            <div class="step-icon">💰</div>
            <div>
              <h3>Step 2: Choose Cryptocurrency</h3>
              <p class="text-muted">Select your preferred digital currency</p>
            </div>
          </div>

          <div class="currency-selector">
            <select id="currency" name="currency" required class="form-input form-select currency-select">
              <option value="">Select cryptocurrency...</option>
              <!-- Options will be populated by JavaScript based on gateway selection -->
            </select>
            <div class="currency-info">
              <small class="text-muted">💡 Available currencies depend on selected payment gateway</small>
            </div>
          </div>
        </div>

        <!-- Step 3: Service & Payment Details -->
        <div class="payment-step-card" id="details-step">
          <div class="step-header">
            <div class="step-icon">📋</div>
            <div>
              <h3>Step 3: Service & Payment Details</h3>
              <p class="text-muted">Choose your service and confirm payment amount</p>
            </div>
          </div>

          <div class="service-selection-section">
            <?php if (!$is_donation): ?>
            <!-- Service Selection First -->
            <div class="service-grid">
              <h4>💼 Choose Your Service</h4>
              <div class="service-cards">
                <div class="service-card" data-service="consulting" data-amount="150" data-type="hourly">
                  <div class="service-icon">💡</div>
                  <h5>Crypto & Forex Consulting</h5>
                  <div class="service-price">$150/hour</div>
                  <p class="service-description">Expert guidance on cryptocurrency and forex trading strategies</p>
                  <div class="service-features">
                    <span class="feature-tag">1-on-1 Session</span>
                    <span class="feature-tag">Strategy Planning</span>
                  </div>
                </div>

                <div class="service-card" data-service="mining-pool" data-amount="200" data-type="yearly">
                  <div class="service-icon">⛏️</div>
                  <h5>Mining Pool Membership</h5>
                  <div class="service-price">$200/year</div>
                  <p class="service-description">Join our exclusive mining pool with premium benefits</p>
                  <div class="service-features">
                    <span class="feature-tag">Annual Access</span>
                    <span class="feature-tag">Priority Support</span>
                  </div>
                </div>

                <div class="service-card" data-service="mining-services" data-amount="500" data-type="monthly">
                  <div class="service-icon">🔧</div>
                  <h5>Mining Services</h5>
                  <div class="service-price">$500/month</div>
                  <p class="service-description">Full-service mining management and optimization</p>
                  <div class="service-features">
                    <span class="feature-tag">Monthly Service</span>
                    <span class="feature-tag">24/7 Monitoring</span>
                  </div>
                </div>

                <div class="service-card" data-service="analysis" data-amount="99" data-type="report">
                  <div class="service-icon">📊</div>
                  <h5>Market Analysis Report</h5>
                  <div class="service-price">$99/report</div>
                  <p class="service-description">Comprehensive market analysis and trading insights</p>
                  <div class="service-features">
                    <span class="feature-tag">Detailed Report</span>
                    <span class="feature-tag">Market Insights</span>
                  </div>
                </div>
              </div>
            </div>
            <?php else: ?>
            <!-- Donation/Investment Options -->
            <div class="investment-grid">
              <h4>💰 Choose Your Contribution</h4>
              <div class="investment-cards">
                <div class="investment-card" data-service="donation-supporter" data-amount="50">
                  <div class="investment-icon">🌱</div>
                  <h5>Supporter</h5>
                  <div class="investment-price">$50+</div>
                  <p class="investment-description">Support our renewable energy research</p>
                </div>

                <div class="investment-card" data-service="donation-advocate" data-amount="500">
                  <div class="investment-icon">🌿</div>
                  <h5>Advocate</h5>
                  <div class="investment-price">$500+</div>
                  <p class="investment-description">Fund equipment upgrades</p>
                </div>

                <div class="investment-card" data-service="donation-champion" data-amount="2500">
                  <div class="investment-icon">🌳</div>
                  <h5>Champion</h5>
                  <div class="investment-price">$2,500+</div>
                  <p class="investment-description">Major renewable energy impact</p>
                </div>

                <div class="investment-card" data-service="investment-equity" data-amount="10000">
                  <div class="investment-icon">🤝</div>
                  <h5>Strategic Investment</h5>
                  <div class="investment-price">$10,000+</div>
                  <p class="investment-description">10% equity stake opportunity</p>
                </div>

                <div class="investment-card" data-service="custom" data-amount="0">
                  <div class="investment-icon">🎯</div>
                  <h5>Custom Amount</h5>
                  <div class="investment-price">Any Amount</div>
                  <p class="investment-description">Choose your own contribution</p>
                </div>
              </div>
            </div>
            <?php endif; ?>

            <!-- Hidden service input for form submission -->
            <input type="hidden" id="service" name="service" value="">
          </div>

          <div class="payment-details-grid">
            <div class="detail-group">
              <label for="amount" class="form-label required">💰 Amount (USD)</label>
              <div class="input-group">
                <div class="input-group-addon">$</div>
                <input type="number" id="amount" name="amount" min="1" step="0.01" required
                       value="<?php echo htmlspecialchars($preset_amount); ?>"
                       class="form-input amount-input" placeholder="0.00">
              </div>
              <div class="amount-info">
                <small class="text-muted" id="amount-description">Select a service to see pricing</small>
              </div>
            </div>

            <div class="detail-group">
              <label for="email" class="form-label required">📧 Email Address</label>
              <input type="email" id="email" name="email" required
                     class="form-input" placeholder="<EMAIL>">
              <small class="text-muted">For payment confirmation and receipt</small>
            </div>

            <div class="detail-group full-width">
              <label for="description" class="form-label">📝 Description/Notes</label>
              <textarea id="description" name="description" rows="4"
                        class="form-input form-textarea"
                        placeholder="<?php echo $is_donation ? "Optional message with your contribution..." : "Additional details about your service requirements..."; ?>"></textarea>
            </div>
          </div>
        </div>

        <!-- Step 4: Review & Submit -->
        <div class="payment-step-card" id="submit-step">
          <div class="step-header">
            <div class="step-icon">🚀</div>
            <div>
              <h3>Step 4: Complete Payment</h3>
              <p class="text-muted">Review your details and proceed to secure payment</p>
            </div>
          </div>

          <div class="payment-summary">
            <div class="summary-item">
              <span class="summary-label">Gateway:</span>
              <span class="summary-value" id="selected-gateway">BTCPay Server</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Currency:</span>
              <span class="summary-value" id="selected-currency">Bitcoin (BTC)</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Amount:</span>
              <span class="summary-value" id="summary-amount">$0.00 USD</span>
            </div>
          </div>

          <div class="submit-section">
            <button type="submit" class="payment-submit-btn">
              <span class="btn-icon">🚀</span>
              <span class="btn-text">Proceed to Secure Payment</span>
            </button>
            <div class="security-notice">
              <small class="text-muted">🔒 Your payment is secured with enterprise-grade encryption</small>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Security & Support -->
    <div class="payment-footer">
      <div class="two-col">
        <div class="card">
          <h4>🔒 Security Features</h4>
          <ul style="list-style: none; padding: 0;">
            <li class="mb-xs">🛡️ Enterprise-grade encryption</li>
            <li class="mb-xs">⚡ Lightning Network support</li>
            <li class="mb-xs">🔐 Self-hosted BTCPay Server</li>
            <li class="mb-xs">🌍 Global payment processing</li>
          </ul>
        </div>
        <div class="card">
          <h4>💡 Need Help?</h4>
          <p class="text-muted">Our support team is available 24/7 to assist with your payment.</p>
          <div class="mt-md">
            <a href="contact.php" class="cta-button secondary">📞 Contact Support</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="text-center mt-xl">
      <div class="flex gap-md justify-center">
        <a href="index.php" class="text-muted">← Back to Homepage</a>
        <span class="text-muted">|</span>
        <a href="services.php" class="text-muted">View Services</a>
      </div>
    </div>
  </section>
</main>

<script>
// Gateway and currency data
const gatewayCurrencies = {
  'btcpay': [
    {value: 'BTC', text: '₿ Bitcoin (BTC) - Lightning & On-chain'}
  ],
  'nowpayments': [
    {value: 'btc', text: '₿ Bitcoin (BTC)'},
    {value: 'eth', text: 'Ξ Ethereum (ETH)'},
    {value: 'usdt', text: '₮ Tether (USDT)'},
    {value: 'usdc', text: '$ USD Coin (USDC)'},
    {value: 'ltc', text: 'Ł Litecoin (LTC)'},
    {value: 'bch', text: '₿ Bitcoin Cash (BCH)'},
    {value: 'trx', text: 'T Tron (TRX)'},
    {value: 'bnb', text: 'B Binance Coin (BNB)'},
    {value: 'ada', text: '₳ Cardano (ADA)'},
    {value: 'dot', text: '● Polkadot (DOT)'},
    {value: 'xrp', text: 'X Ripple (XRP)'},
    {value: 'matic', text: 'M Polygon (MATIC)'}
  ]
};

function selectGateway(gatewayId) {
  // Update radio button
  document.getElementById('gateway_' + gatewayId).checked = true;

  // Update visual selection
  document.querySelectorAll('.gateway-card').forEach(card => {
    card.classList.remove('selected');
  });

  // Highlight selected option
  event.currentTarget.classList.add('selected');

  // Update summary
  const gatewayNames = {
    'btcpay': 'BTCPay Server',
    'nowpayments': 'NowPayments'
  };
  document.getElementById('selected-gateway').textContent = gatewayNames[gatewayId];

  // Show gateway details
  showGatewayDetails(gatewayId);

  // Update currency options
  updateCurrencyOptions(gatewayId);

  // Activate next step
  activateStep(2);
}

function showGatewayDetails(gatewayId) {
  const detailsDiv = document.getElementById('gateway-details');
  const contentDiv = document.getElementById('gateway-info-content');

  const gatewayInfo = {
    'btcpay': {
      title: '⚡ BTCPay Server',
      description: 'Self-hosted Bitcoin payment processor with Lightning Network support',
      features: ['No transaction fees', 'Lightning Network instant payments', 'Self-hosted & private', 'Bitcoin only']
    },
    'nowpayments': {
      title: '🌐 NowPayments',
      description: 'Multi-cryptocurrency payment gateway with global support',
      features: ['300+ cryptocurrencies', 'Low fees (0.5-1.5%)', 'Global support', 'Easy integration']
    }
  };

  if (gatewayInfo[gatewayId]) {
    const info = gatewayInfo[gatewayId];
    contentDiv.innerHTML = `
      <div class="alert alert-info">
        <h4>${info.title}</h4>
        <p>${info.description}</p>
        <ul style="list-style: none; padding: 0; margin-top: var(--space-sm);">
          ${info.features.map(feature => `<li style="margin-bottom: var(--space-xs);">✅ ${feature}</li>`).join('')}
        </ul>
      </div>
    `;
    detailsDiv.classList.add('show');
  }
}

function activateStep(stepNumber) {
  // Update step indicators
  document.querySelectorAll('.step').forEach((step, index) => {
    if (index + 1 <= stepNumber) {
      step.classList.add('active');
    } else {
      step.classList.remove('active');
    }
  });

  // Update step cards
  document.querySelectorAll('.payment-step-card').forEach((card, index) => {
    if (index + 1 === stepNumber) {
      card.classList.add('active');
    } else {
      card.classList.remove('active');
    }
  });
}

function updatePaymentSummary() {
  // Update currency in summary
  const currencySelect = document.getElementById('currency');
  if (currencySelect.value) {
    document.getElementById('selected-currency').textContent = currencySelect.options[currencySelect.selectedIndex].text;
    activateStep(3);
  }

  // Update amount in summary
  const amountInput = document.getElementById('amount');
  if (amountInput.value) {
    document.getElementById('summary-amount').textContent = '$' + parseFloat(amountInput.value).toFixed(2) + ' USD';
    activateStep(4);
  }
}

function updateCurrencyOptions(gatewayId) {
  const currencySelect = document.getElementById('currency');
  currencySelect.innerHTML = '<option value="">Select cryptocurrency...</option>';

  if (gatewayCurrencies[gatewayId]) {
    gatewayCurrencies[gatewayId].forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.value;
      option.textContent = currency.text;
      currencySelect.appendChild(option);
    });
  }
}

// Service selection and autofill functionality
function selectService(serviceCard) {
  // Remove selection from all cards
  document.querySelectorAll('.service-card, .investment-card').forEach(card => {
    card.classList.remove('selected');
  });

  // Select current card
  serviceCard.classList.add('selected');

  // Get service data
  const serviceType = serviceCard.dataset.service;
  const amount = parseFloat(serviceCard.dataset.amount);
  const type = serviceCard.dataset.type;

  // Update hidden service input
  document.getElementById('service').value = serviceType;

  // Auto-fill amount if not custom
  const amountInput = document.getElementById('amount');
  const amountInfo = document.getElementById('amount-description');

  if (amount > 0) {
    amountInput.value = amount;
    amountInfo.textContent = `Auto-filled from ${serviceCard.querySelector('h5').textContent}`;
    amountInfo.classList.add('auto-filled');
  } else {
    amountInput.value = '';
    amountInfo.textContent = 'Enter your custom amount';
    amountInfo.classList.remove('auto-filled');
  }

  // Update payment summary
  updatePaymentSummary();

  // Update available payment methods based on service
  updateAvailablePaymentMethods(serviceType, amount);

  // Activate step 3 (payment details)
  activateStep(3);
}

// Dynamic payment method availability
function updateAvailablePaymentMethods(serviceType, amount) {
  const btcpayCard = document.querySelector('[data-gateway="btcpay"]');
  const nowpaymentsCard = document.querySelector('[data-gateway="nowpayments"]');

  // Reset all gateways to available
  document.querySelectorAll('.gateway-card').forEach(card => {
    card.classList.remove('disabled');
    card.style.opacity = '1';
  });

  // Apply service-specific restrictions
  if (serviceType === 'investment-equity') {
    // For equity investments, prefer BTCPay for privacy
    if (btcpayCard) {
      btcpayCard.querySelector('.gateway-feature').textContent = '🔐 Recommended for Investments';
    }
  } else if (amount >= 10000) {
    // For large amounts, highlight security features
    if (btcpayCard) {
      btcpayCard.querySelector('.gateway-feature').textContent = '🔐 Best for Large Amounts';
    }
  } else if (amount < 50) {
    // For small amounts, highlight low fees
    if (nowpaymentsCard) {
      nowpaymentsCard.querySelector('.gateway-feature').textContent = '💰 Low Fees for Small Amounts';
    }
  }

  // Update gateway recommendations
  updateGatewayRecommendations(serviceType, amount);
}

function updateGatewayRecommendations(serviceType, amount) {
  const recommendations = {
    'consulting': 'Professional services - both gateways suitable',
    'mining-pool': 'Recurring service - BTCPay recommended for privacy',
    'mining-services': 'Enterprise service - BTCPay recommended',
    'analysis': 'Digital product - both gateways suitable',
    'investment-equity': 'Investment - BTCPay strongly recommended for privacy',
    'donation-supporter': 'Small donation - NowPayments for variety',
    'donation-advocate': 'Medium donation - both gateways suitable',
    'donation-champion': 'Large donation - BTCPay recommended',
    'custom': 'Custom amount - choose based on preference'
  };

  // You could display these recommendations in the UI if desired
  console.log(`Recommendation for ${serviceType}: ${recommendations[serviceType] || 'Both gateways suitable'}`);
}

// Enhanced payment summary with service info
function updatePaymentSummary() {
  // Update currency in summary
  const currencySelect = document.getElementById('currency');
  if (currencySelect.value) {
    document.getElementById('selected-currency').textContent = currencySelect.options[currencySelect.selectedIndex].text;
  }

  // Update amount in summary
  const amountInput = document.getElementById('amount');
  if (amountInput.value) {
    document.getElementById('summary-amount').textContent = '$' + parseFloat(amountInput.value).toFixed(2) + ' USD';
  }

  // Update service in summary (add service row if it doesn't exist)
  const selectedService = document.querySelector('.service-card.selected, .investment-card.selected');
  if (selectedService) {
    const serviceName = selectedService.querySelector('h5').textContent;

    // Add service row to summary if it doesn't exist
    let serviceRow = document.querySelector('.summary-item.service-row');
    if (!serviceRow) {
      serviceRow = document.createElement('div');
      serviceRow.className = 'summary-item service-row';
      serviceRow.innerHTML = `
        <span class="summary-label">Service:</span>
        <span class="summary-value" id="selected-service"></span>
      `;
      // Insert before amount row
      const amountRow = document.querySelector('.summary-item:last-child');
      amountRow.parentNode.insertBefore(serviceRow, amountRow);
    }

    document.getElementById('selected-service').textContent = serviceName;
    activateStep(4);
  }
}

// Initialize with default gateway and add event listeners
document.addEventListener('DOMContentLoaded', function() {
  const defaultGateway = document.querySelector('input[name="gateway"]:checked').value;
  updateCurrencyOptions(defaultGateway);

  // Initialize with default gateway selected
  const defaultCard = document.querySelector(`input[value="${defaultGateway}"]`).closest('.gateway-card');
  if (defaultCard) {
    defaultCard.classList.add('selected');
    showGatewayDetails(defaultGateway);
  }

  // Add event listeners for form interactions
  document.getElementById('currency').addEventListener('change', updatePaymentSummary);
  document.getElementById('amount').addEventListener('input', updatePaymentSummary);

  // Add service card click listeners
  document.querySelectorAll('.service-card, .investment-card').forEach(card => {
    card.addEventListener('click', function() {
      selectService(this);
    });
  });

  // Initialize summary with default values
  updatePaymentSummary();
});
</script>

<style>
/* Payment Form Structure */
.payment-form-container {
  max-width: 800px;
  margin: 0 auto;
}

.payment-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

/* Payment Steps */
.payment-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  margin: var(--space-xl) 0;
  padding: var(--space-lg);
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  opacity: 0.5;
  transition: all var(--transition-base);
}

.step.active {
  opacity: 1;
  color: var(--primary-color);
}

.step-number {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all var(--transition-base);
}

.step.active .step-number {
  background: var(--primary-gradient);
  color: white;
  transform: scale(1.1);
}

.step-title {
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}

.step-connector {
  width: 3rem;
  height: 2px;
  background: var(--border-color);
  opacity: 0.5;
}

/* Payment Step Cards - Refined */
.payment-step-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.payment-step-card.active {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.step-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-sm);
  border-bottom: 1px solid var(--border-color);
}

.step-icon {
  font-size: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  border-radius: 50%;
  color: white;
}

/* Gateway Grid - Compact Design */
.gateway-grid {
  display: flex;
  gap: var(--space-md);
  margin: var(--space-lg) 0;
  justify-content: center;
}

.gateway-card {
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-base);
  background: var(--surface-color);
  flex: 1;
  max-width: 200px;
  min-width: 160px;
}

.gateway-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.gateway-card input[type="radio"]:checked + label .gateway-card,
.gateway-card.selected {
  border-color: var(--primary-color);
  background: var(--hover-bg);
  box-shadow: var(--shadow-sm);
}

.gateway-icon {
  font-size: 1.8rem;
  margin-bottom: var(--space-xs);
}

.gateway-card h4 {
  font-size: 0.95rem;
  margin-bottom: var(--space-xs);
}

.gateway-description {
  color: var(--text-muted);
  font-size: 0.8rem;
  margin: var(--space-xs) 0;
}

.gateway-feature {
  font-size: 0.7rem;
  color: var(--primary-color);
  font-weight: 500;
}

/* Currency Selector */
.currency-selector {
  max-width: 400px;
  margin: 0 auto;
}

.currency-select {
  font-size: 1.1rem;
  padding: var(--space-lg);
}

.currency-info {
  text-align: center;
  margin-top: var(--space-md);
}

/* Service Selection - Compact */
.service-selection-section {
  margin-bottom: var(--space-lg);
}

.service-grid h4,
.investment-grid h4 {
  text-align: center;
  margin-bottom: var(--space-md);
  color: var(--text-primary);
  font-size: 1.1rem;
}

.service-cards,
.investment-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.service-card,
.investment-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-base);
  background: var(--surface-color);
  position: relative;
}

.service-card:hover,
.investment-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.service-card.selected,
.investment-card.selected {
  border-color: var(--primary-color);
  background: var(--hover-bg);
  box-shadow: var(--shadow-sm);
}

.service-card.selected::after,
.investment-card.selected::after {
  content: '✓';
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 1.5rem;
  height: 1.5rem;
  background: var(--primary-gradient);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.8rem;
}

.service-icon,
.investment-icon {
  font-size: 2.2rem;
  margin-bottom: var(--space-sm);
}

.service-card h5,
.investment-card h5 {
  margin-bottom: var(--space-xs);
  color: var(--text-primary);
  font-size: 1rem;
}

.service-price,
.investment-price {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: var(--space-sm);
}

.service-description,
.investment-description {
  color: var(--text-muted);
  font-size: 0.85rem;
  margin-bottom: var(--space-sm);
  line-height: 1.3;
}

.service-features,
.investment-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  justify-content: center;
}

.feature-tag {
  background: var(--hover-bg);
  color: var(--text-primary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.7rem;
  font-weight: 500;
}

/* Payment Details Grid - Aligned & Compact */
.payment-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border-color);
  align-items: start;
}

.detail-group.full-width {
  grid-column: 1 / -1;
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.detail-group .form-label {
  margin-bottom: var(--space-xs);
  font-size: 0.9rem;
}

.detail-group .form-input {
  padding: var(--space-sm) var(--space-md);
  font-size: 0.95rem;
}

.amount-input {
  font-size: 1.1rem;
  font-weight: 600;
}

.amount-info {
  margin-top: var(--space-xs);
  min-height: 1.2rem;
}

.amount-info.auto-filled {
  color: var(--primary-color);
  font-weight: 500;
}

.amount-info.auto-filled::before {
  content: '✨ ';
}

/* Input Group Refinements */
.input-group-addon {
  padding: var(--space-sm) var(--space-md);
  font-size: 0.95rem;
}

/* Payment Summary */
.payment-summary {
  background: var(--hover-bg);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
  border-bottom: none;
  font-weight: 600;
  font-size: 1.1rem;
}

.summary-label {
  color: var(--text-muted);
}

.summary-value {
  color: var(--text-primary);
  font-weight: 500;
}

/* Submit Section */
.submit-section {
  text-align: center;
}

.payment-submit-btn {
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-lg) var(--space-xl);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  min-width: 250px;
  justify-content: center;
}

.payment-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-icon {
  font-size: 1.2rem;
}

.security-notice {
  margin-top: var(--space-md);
}

/* Payment Footer */
.payment-footer {
  margin-top: var(--space-xl);
  padding-top: var(--space-xl);
  border-top: 1px solid var(--border-color);
}

/* Gateway Details */
.gateway-details {
  margin-top: var(--space-lg);
  display: none;
}

.gateway-details.show {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-steps {
    flex-direction: column;
    gap: var(--space-sm);
  }

  .step-connector {
    width: 2px;
    height: 2rem;
  }

  .gateway-grid {
    grid-template-columns: 1fr;
  }

  .service-cards,
  .investment-cards {
    grid-template-columns: 1fr;
  }

  .payment-details-grid {
    grid-template-columns: 1fr;
  }

  .step-header {
    flex-direction: column;
    text-align: center;
  }

  .service-card,
  .investment-card {
    padding: var(--space-md);
  }

  .service-icon,
  .investment-icon {
    font-size: 2.5rem;
  }

  .service-price,
  .investment-price {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .payment-form-container {
    padding: 0 var(--space-sm);
  }

  .payment-step-card {
    padding: var(--space-lg);
  }

  .service-cards,
  .investment-cards {
    gap: var(--space-md);
  }
}
</style>

<?php include "footer.php"; ?>
