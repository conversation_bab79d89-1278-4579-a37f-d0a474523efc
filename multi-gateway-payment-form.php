<?php
require_once "includes/payment-gateway-manager.php";
include "header.php";

$payment_type = $_GET["type"] ?? "service";
$is_donation = ($payment_type === "donation");
$preset_amount = $_GET["amount"] ?? "";

$gatewayManager = new Payment_Gateway_Manager();
$availableGateways = $gatewayManager->getAvailableGateways();
?>

<main>
  <section class="hero">
    <div class="hero-content">
      <h1><?php echo $is_donation ? "💝 Make a Cryptocurrency Donation" : "💳 Pay with Cryptocurrency"; ?></h1>
      <p><?php echo $is_donation ? "Support our sustainable mining mission with secure crypto donations" : "Secure cryptocurrency payments for our professional services"; ?></p>
      <div class="flex gap-md justify-center mt-xl">
        <a href="#payment-form" class="cta-button">💳 Start Payment</a>
        <a href="#gateway-comparison" class="cta-button secondary">🔍 Compare Options</a>
      </div>
    </div>
  </section>

  <!-- Payment Stats -->
  <section class="section">
    <div class="three-col">
      <div class="card text-center">
        <h3>🔒 100%</h3>
        <p class="text-muted">Secure Processing</p>
      </div>
      <div class="card text-center">
        <h3>⚡ Instant</h3>
        <p class="text-muted">Lightning Network</p>
      </div>
      <div class="card text-center">
        <h3>🌍 300+</h3>
        <p class="text-muted">Cryptocurrencies</p>
      </div>
    </div>
  </section>

  <section class="section" id="payment-form">
    <div class="two-col">
      <div>
        <h2>💳 Choose Your Payment Method</h2>
        <p>Select from our secure payment gateways and complete your transaction with confidence.</p>
        <div class="alert alert-success">
          <strong>🚀 Multi-Gateway Support:</strong> Choose the option that works best for you
        </div>
      </div>
      <div class="card">
        <h3>🔒 Security Features</h3>
        <ul style="list-style: none; padding: 0;">
          <li class="mb-sm">🛡️ Enterprise-grade encryption</li>
          <li class="mb-sm">⚡ Lightning Network support</li>
          <li class="mb-sm">🔐 Self-hosted BTCPay Server</li>
          <li class="mb-sm">🌍 Global payment processing</li>
          <li class="mb-sm">📧 Instant confirmations</li>
        </ul>
      </div>
    </div>

    <?php
    // Display error messages if any
    if (isset($_GET['error'])) {
        $error = $_GET['error'];
        $details = $_GET['details'] ?? '';

        echo '<div class="alert alert-error">';
        echo '<h3>❌ Payment Error</h3>';
        echo '<p>' . htmlspecialchars($error) . '</p>';
        if ($details) echo '<p><strong>Details:</strong> ' . htmlspecialchars($details) . '</p>';
        echo '</div>';
    }
    ?>

    <div class="alert alert-info">
        <h3>🚀 Multiple Payment Options Available</h3>
        <p>Choose from Bitcoin-only payments via our self-hosted BTCPay Server, or select from 300+ cryptocurrencies via NowPayments.</p>
    </div>

    <div class="card">
      <form method="POST" action="multi-gateway-process-payment.php">
        <input type="hidden" name="payment_type" value="<?php echo htmlspecialchars($payment_type); ?>">

        <!-- Payment Gateway Selection -->
        <div class="form-group">
          <h3>🔧 Step 1: Choose Payment Gateway</h3>
          <div class="two-col">

            <?php foreach ($availableGateways as $gatewayId => $gateway): ?>
            <div class="card gateway-option" style="cursor: pointer; transition: all var(--transition-base);"
                 onclick="selectGateway('<?php echo $gatewayId; ?>')">
              <input type="radio" name="gateway" value="<?php echo $gatewayId; ?>" id="gateway_<?php echo $gatewayId; ?>"
                     style="margin-bottom: var(--space-sm);" <?php echo $gatewayId === 'btcpay' ? 'checked' : ''; ?>>
              <label for="gateway_<?php echo $gatewayId; ?>" style="cursor: pointer;">
                <h4><?php echo $gateway['icon']; ?> <?php echo $gateway['name']; ?></h4>
                <p class="text-muted mb-md"><?php echo $gateway['description']; ?></p>
                <div class="mb-md">
                  <strong>Features:</strong>
                  <ul style="list-style: none; padding: 0; margin-top: var(--space-xs);">
                    <?php foreach ($gateway['features'] as $feature): ?>
                    <li class="mb-xs">✅ <?php echo $feature; ?></li>
                    <?php endforeach; ?>
                  </ul>
                </div>
                <div class="mb-md">
                  <strong>Currencies:</strong>
                  <span class="text-muted" style="font-size: 0.9em;">
                    <?php echo implode(', ', array_slice($gateway['supported_currencies'], 0, 5)); ?>
                    <?php if (count($gateway['supported_currencies']) > 5): ?>
                    + <?php echo count($gateway['supported_currencies']) - 5; ?> more
                    <?php endif; ?>
                  </span>
                </div>
                <div class="badge badge-primary">Recommended</div>
              </label>
            </div>
            <?php endforeach; ?>

          </div>
        </div>

        <!-- Currency Selection -->
        <div class="form-group">
          <h3>💰 Step 2: Choose Cryptocurrency</h3>
          <label class="form-label">Select your preferred cryptocurrency:</label>
          <select id="currency" name="currency" required class="form-input form-select">
            <option value="">Select cryptocurrency...</option>
            <!-- Options will be populated by JavaScript based on gateway selection -->
          </select>
          <small class="text-muted">Available currencies depend on selected payment gateway</small>
        </div>

        <!-- Payment Details -->
        <div class="form-group">
          <h3>📋 Step 3: Payment Details</h3>

          <div class="form-group">
            <label for="amount" class="form-label">Amount (USD):</label>
            <input type="number" id="amount" name="amount" min="1" step="0.01" required
                   value="<?php echo htmlspecialchars($preset_amount); ?>"
                   class="form-input" placeholder="Enter amount in USD">
            <small class="text-muted">Minimum: $1.00 USD</small>
          </div>

        <div style="margin-bottom: 20px;">
          <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Address:</label>
          <input type="email" id="email" name="email" required
                 style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                 placeholder="<EMAIL>">
          <small style="color: #666;">For payment confirmation and receipt</small>
        </div>

        <?php if (!$is_donation): ?>
        <div style="margin-bottom: 20px;">
          <label for="service" style="display: block; margin-bottom: 5px; font-weight: bold;">Service Type:</label>
          <select id="service" name="service" required
                  style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
            <option value="consulting">Crypto & Forex Consulting ($150/hour)</option>
            <option value="mining-pool">Mining Pool Membership ($200/year)</option>
            <option value="mining-services">Mining Services ($500/month)</option>
            <option value="analysis">Market Analysis Report ($99/report)</option>
            <option value="other">Other Services</option>
          </select>
        </div>
        <?php endif; ?>

        <div style="margin-bottom: 20px;">
          <label for="description" style="display: block; margin-bottom: 5px; font-weight: bold;">Description/Notes:</label>
          <textarea id="description" name="description" rows="3"
                    style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 5px;"
                    placeholder="<?php echo $is_donation ? "Optional message with your donation" : "Describe the service you are paying for"; ?>"></textarea>
        </div>
      </div>

      <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h4>💡 Payment Gateway Comparison:</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
          <div>
            <strong>⚡ BTCPay Server:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
              <li>No transaction fees</li>
              <li>Lightning Network (instant)</li>
              <li>Self-hosted & private</li>
              <li>Bitcoin only</li>
            </ul>
          </div>
          <div>
            <strong>🌐 NowPayments:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
              <li>300+ cryptocurrencies</li>
              <li>Low fees (0.5-1.5%)</li>
              <li>Global support</li>
              <li>Easy integration</li>
            </ul>
          </div>
        </div>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <button type="submit" class="cta-button" style="padding: 15px 30px; font-size: 1.1em;">
          🚀 Proceed to Payment
        </button>
      </div>
    </form>

    <div style="text-align: center; margin-top: 30px;">
      <p><a href="index.php">← Back to Homepage</a></p>
    </div>
  </section>
</main>

<script>
// Gateway and currency data
const gatewayCurrencies = {
  'btcpay': [
    {value: 'BTC', text: '₿ Bitcoin (BTC) - Lightning & On-chain'}
  ],
  'nowpayments': [
    {value: 'btc', text: '₿ Bitcoin (BTC)'},
    {value: 'eth', text: 'Ξ Ethereum (ETH)'},
    {value: 'usdt', text: '₮ Tether (USDT)'},
    {value: 'usdc', text: '$ USD Coin (USDC)'},
    {value: 'ltc', text: 'Ł Litecoin (LTC)'},
    {value: 'bch', text: '₿ Bitcoin Cash (BCH)'},
    {value: 'trx', text: 'T Tron (TRX)'},
    {value: 'bnb', text: 'B Binance Coin (BNB)'},
    {value: 'ada', text: '₳ Cardano (ADA)'},
    {value: 'dot', text: '● Polkadot (DOT)'},
    {value: 'xrp', text: 'X Ripple (XRP)'},
    {value: 'matic', text: 'M Polygon (MATIC)'}
  ]
};

function selectGateway(gatewayId) {
  // Update radio button
  document.getElementById('gateway_' + gatewayId).checked = true;

  // Update visual selection
  document.querySelectorAll('.gateway-option').forEach(option => {
    option.style.borderColor = '#ddd';
    option.style.backgroundColor = 'transparent';
  });

  event.currentTarget.style.borderColor = '#0077cc';
  event.currentTarget.style.backgroundColor = '#f0f8ff';

  // Update currency options
  updateCurrencyOptions(gatewayId);
}

function updateCurrencyOptions(gatewayId) {
  const currencySelect = document.getElementById('currency');
  currencySelect.innerHTML = '<option value="">Select cryptocurrency...</option>';

  if (gatewayCurrencies[gatewayId]) {
    gatewayCurrencies[gatewayId].forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.value;
      option.textContent = currency.text;
      currencySelect.appendChild(option);
    });
  }
}

// Initialize with default gateway
document.addEventListener('DOMContentLoaded', function() {
  const defaultGateway = document.querySelector('input[name="gateway"]:checked').value;
  updateCurrencyOptions(defaultGateway);

  // Add click handlers to gateway options
  document.querySelectorAll('input[name="gateway"]').forEach(radio => {
    radio.addEventListener('change', function() {
      updateCurrencyOptions(this.value);
    });
  });
});
</script>

<style>
.gateway-option:hover {
  border-color: #0077cc !important;
  background-color: #f8f9fa !important;
}

.gateway-option input[type="radio"]:checked + label {
  color: #0077cc;
}
</style>

<?php include "footer.php"; ?>
